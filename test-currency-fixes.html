<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Corrections de Devise - SmartBoutique</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        .profit-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .profit-table th,
        .profit-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .profit-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }
        .profit-table td:last-child {
            text-align: right;
        }
        .currency-primary {
            font-weight: medium;
            color: #333;
        }
        .currency-secondary {
            font-size: 0.875rem;
            color: #666;
        }
        .input-group {
            margin-bottom: 20px;
        }
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: medium;
            color: #333;
        }
        .currency-toggle {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        .toggle-btn {
            padding: 8px 16px;
            border: 2px solid #1976d2;
            background: white;
            color: #1976d2;
            border-radius: 6px;
            cursor: pointer;
            font-weight: medium;
            transition: all 0.2s;
        }
        .toggle-btn.active {
            background: #1976d2;
            color: white;
        }
        .toggle-btn:hover {
            background: #1565c0;
            color: white;
        }
        .currency-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            font-family: monospace;
        }
        .currency-input:focus {
            outline: none;
            border-color: #1976d2;
        }
        .result-display {
            margin-top: 15px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 6px;
            border-left: 4px solid #1976d2;
        }
        .success { color: #4caf50; font-weight: bold; }
        .info { color: #2196f3; }
        .warning { color: #ff9800; }
        .error { color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test des Corrections de Devise - SmartBoutique</h1>
        <p>Test des deux améliorations apportées à la gestion des devises dans SmartBoutique.</p>
    </div>

    <div class="container">
        <div class="test-section">
            <div class="test-title">💰 Test 1: Affichage Dual Currency dans le Tableau des Profits Potentiels</div>
            <p>Vérification que les profits potentiels affichent maintenant CDF et USD.</p>
            
            <table class="profit-table">
                <thead>
                    <tr>
                        <th>Produit</th>
                        <th>Profit Potentiel</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div><strong>Smartphone Samsung Galaxy</strong></div>
                            <div style="font-size: 0.875rem; color: #666;">Stock: 15</div>
                        </td>
                        <td>
                            <div class="currency-primary" id="profit-1-cdf">420 000 CDF</div>
                            <div class="currency-secondary" id="profit-1-usd">≈ $150.00</div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div><strong>Ordinateur Portable HP</strong></div>
                            <div style="font-size: 0.875rem; color: #666;">Stock: 8</div>
                        </td>
                        <td>
                            <div class="currency-primary" id="profit-2-cdf">2 800 000 CDF</div>
                            <div class="currency-secondary" id="profit-2-usd">≈ $1,000.00</div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div><strong>Télévision LG 55"</strong></div>
                            <div style="font-size: 0.875rem; color: #666;">Stock: 5</div>
                        </td>
                        <td>
                            <div class="currency-primary" id="profit-3-cdf">1 400 000 CDF</div>
                            <div class="currency-secondary" id="profit-3-usd">≈ $500.00</div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div class="result-display">
                <span class="success">✅ Correction réussie!</span> Le tableau affiche maintenant les profits potentiels en CDF (primaire) et USD (secondaire) avec le format "≈ $XXX.XX".
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">⌨️ Test 2: Saisie USD Flexible (Sans Décimales Automatiques)</div>
            <p>Vérification que les champs USD permettent la saisie libre sans forcer les décimales.</p>
            
            <div class="currency-toggle">
                <button class="toggle-btn active" onclick="switchMode('CDF')">Saisie en CDF</button>
                <button class="toggle-btn" onclick="switchMode('USD')">Saisie en USD</button>
            </div>

            <div class="input-group">
                <label for="currency-input">Montant (<span id="current-mode">CDF</span>)</label>
                <input type="text" id="currency-input" class="currency-input" 
                       placeholder="Tapez un montant..." 
                       oninput="handleInput()" 
                       pattern="[0-9]*\.?[0-9]*" 
                       inputmode="decimal">
            </div>

            <div class="result-display">
                <div><strong>Valeur saisie:</strong> <span id="input-value">0</span></div>
                <div><strong>Conversion:</strong> <span id="conversion">0 CDF ≈ $0.00</span></div>
                <div><strong>Statut:</strong> <span id="status" class="info">ℹ️ Prêt pour la saisie</span></div>
            </div>

            <div style="margin-top: 20px;">
                <h4>🧪 Tests de Saisie USD:</h4>
                <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 10px;">
                    <button onclick="testUSDInput('50')" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">Test: 50</button>
                    <button onclick="testUSDInput('100')" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">Test: 100</button>
                    <button onclick="testUSDInput('1234')" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">Test: 1234</button>
                    <button onclick="testUSDInput('50.25')" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">Test: 50.25</button>
                    <button onclick="testUSDInput('999.99')" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;">Test: 999.99</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentMode = 'CDF';
        const exchangeRate = 2800;

        function switchMode(mode) {
            currentMode = mode;
            document.getElementById('current-mode').textContent = mode;
            
            // Update button states
            document.querySelectorAll('.toggle-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update input placeholder
            const input = document.getElementById('currency-input');
            if (mode === 'USD') {
                input.placeholder = "Ex: 53, 100, 1234.56 (pas de .00 forcé)";
            } else {
                input.placeholder = "Ex: 150000, 2800000";
            }
            
            handleInput(); // Refresh conversion
        }

        function handleInput() {
            const input = document.getElementById('currency-input');
            const value = input.value;
            
            // Update display
            document.getElementById('input-value').textContent = value || '0';
            
            // Validate and convert
            if (value === '' || /^\d*\.?\d*$/.test(value)) {
                const numericValue = parseFloat(value) || 0;
                
                if (!isNaN(numericValue) && numericValue >= 0) {
                    // Convert and display
                    let cdfValue, usdValue;
                    if (currentMode === 'CDF') {
                        cdfValue = numericValue;
                        usdValue = cdfValue / exchangeRate;
                    } else {
                        usdValue = numericValue;
                        cdfValue = usdValue * exchangeRate;
                    }
                    
                    document.getElementById('conversion').textContent = 
                        `${Math.round(cdfValue).toLocaleString('fr-FR')} CDF ≈ $${usdValue.toFixed(2)}`;
                    document.getElementById('status').innerHTML = 
                        '<span class="success">✅ Saisie valide - Conversion réussie</span>';
                } else {
                    document.getElementById('status').innerHTML = 
                        '<span class="info">ℹ️ Saisie en cours...</span>';
                }
            } else {
                document.getElementById('status').innerHTML = 
                    '<span style="color: orange;">⚠️ Format non valide</span>';
            }
        }

        function testUSDInput(testValue) {
            // Switch to USD mode
            switchMode('USD');
            
            // Set the test value
            const input = document.getElementById('currency-input');
            input.value = testValue;
            
            // Trigger input handling
            handleInput();
            
            // Show test result
            setTimeout(() => {
                const statusElement = document.getElementById('status');
                statusElement.innerHTML = `<span class="success">✅ Test "${testValue}" - Saisie libre sans décimales forcées!</span>`;
            }, 100);
        }

        // Initialize displays
        handleInput();
    </script>
</body>
</html>
