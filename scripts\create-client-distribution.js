#!/usr/bin/env node

/**
 * SmartBoutique Client Distribution Creator
 * 
 * Creates production-ready distribution files for Windows clients:
 * - NSIS Installer (.exe)
 * - Portable Application (.exe)
 * - Client documentation and instructions
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 SmartBoutique - Client Distribution Creator');
console.log('==============================================\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Read package.json to get version
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
console.log(`📦 Creating distribution for SmartBoutique v${packageJson.version}`);
console.log(`📝 ${packageJson.description}\n`);

try {
  // Step 1: Clean previous builds
  console.log('🧹 Step 1: Cleaning previous builds...');
  const dirsToClean = ['dist', 'dist-electron', 'release-final'];
  dirsToClean.forEach(dir => {
    if (fs.existsSync(dir)) {
      console.log(`   Removing ${dir}/`);
      execSync(`rmdir /s /q ${dir}`, { stdio: 'inherit', shell: true });
    }
  });

  // Step 2: Install/verify dependencies
  console.log('\n📥 Step 2: Verifying dependencies...');
  if (!fs.existsSync('node_modules')) {
    console.log('   Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  } else {
    console.log('   ✅ Dependencies already installed');
  }

  // Step 3: Run production build
  console.log('\n🔨 Step 3: Building application...');
  console.log('   Building Vite frontend...');
  execSync('npm run build:vite', { stdio: 'inherit' });
  
  console.log('   Building Electron main process...');
  execSync('npm run build:electron', { stdio: 'inherit' });

  // Step 4: Verify build files
  console.log('\n✅ Step 4: Verifying build files...');
  const requiredFiles = [
    'dist/index.html',
    'dist-electron/main.js'
  ];
  
  requiredFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      throw new Error(`Required build file missing: ${file}`);
    }
    console.log(`   ✅ ${file}`);
  });

  // Step 5: Create Windows distributions
  console.log('\n📦 Step 5: Creating Windows distributions...');

  console.log('   Creating NSIS Installer...');
  execSync('npx electron-builder --win nsis', { stdio: 'inherit' });

  console.log('   Creating Portable Application...');
  execSync('npx electron-builder --win portable', { stdio: 'inherit' });

  // Step 6: Create client documentation
  console.log('\n📝 Step 6: Creating client documentation...');
  createClientDocumentation(packageJson.version);

  // Step 7: Verify distribution files
  console.log('\n🔍 Step 7: Verifying distribution files...');
  const releaseDir = 'release-final';
  if (!fs.existsSync(releaseDir)) {
    throw new Error('Release directory not found');
  }

  const files = fs.readdirSync(releaseDir);
  const installerFile = files.find(f => f.includes('Installer') && f.endsWith('.exe'));
  const portableFile = files.find(f => f.includes('Portable') && f.endsWith('.exe'));

  if (!installerFile) {
    throw new Error('Installer file not found in release directory');
  }
  if (!portableFile) {
    throw new Error('Portable file not found in release directory');
  }

  console.log(`   ✅ Installer: ${installerFile}`);
  console.log(`   ✅ Portable: ${portableFile}`);

  // Step 8: Success summary
  console.log('\n🎉 SUCCESS! Client distribution created successfully!');
  console.log('\n📋 Distribution Summary:');
  console.log(`   📁 Location: ${path.resolve(releaseDir)}`);
  console.log(`   📦 Installer: ${installerFile}`);
  console.log(`   🎒 Portable: ${portableFile}`);
  console.log(`   📄 Documentation: CLIENT_INSTRUCTIONS.md`);
  
  console.log('\n🚀 Ready for Client Distribution:');
  console.log('   • Send installer for permanent installation');
  console.log('   • Send portable for testing without installation');
  console.log('   • Include CLIENT_INSTRUCTIONS.md for setup guidance');

} catch (error) {
  console.error('\n❌ Distribution creation failed:', error.message);
  console.error('\n🔧 Troubleshooting:');
  console.error('   • Ensure all dependencies are installed: npm install');
  console.error('   • Check that electron-builder is properly configured');
  console.error('   • Verify build files exist before packaging');
  process.exit(1);
}

function createClientDocumentation(version) {
  const clientInstructions = `# SmartBoutique v${version} - Instructions Client

## 🎯 À Propos de SmartBoutique

SmartBoutique est une application de gestion complète pour boutiques et commerces de détail, développée spécialement pour les entreprises en République Démocratique du Congo.

### ✨ Fonctionnalités Principales:
- **Gestion des ventes** avec support CDF/USD
- **Gestion des stocks** et inventaire
- **Gestion des clients** et créances
- **Impression de reçus** thermiques
- **Tableau de bord** avec statistiques
- **Interface en français** adaptée au marché local
- **Fonctionnement hors ligne** complet

## 📦 Options d'Installation

### Option 1: Installation Complète (Recommandée)
**Fichier**: \`SmartBoutique-Installer-${version}.exe\`

1. **Télécharger** le fichier d'installation
2. **Clic droit** → "Exécuter en tant qu'administrateur"
3. **Suivre** l'assistant d'installation
4. **Choisir** le répertoire d'installation
5. **Créer** les raccourcis bureau/menu démarrer
6. **Lancer** SmartBoutique depuis le raccourci

**Avantages:**
- Installation propre dans le système
- Raccourcis automatiques
- Désinstallation facile
- Mises à jour futures simplifiées

### Option 2: Version Portable (Test Rapide)
**Fichier**: \`SmartBoutique-Portable-${version}.exe\`

1. **Télécharger** le fichier portable
2. **Créer** un dossier (ex: "SmartBoutique")
3. **Déplacer** le fichier dans ce dossier
4. **Double-cliquer** pour lancer directement

**Avantages:**
- Aucune installation requise
- Parfait pour tester l'application
- Peut fonctionner depuis une clé USB
- Ne modifie pas le système

## 🚀 Premier Démarrage

### Configuration Initiale:
1. **Lancer** SmartBoutique
2. **Configurer** les informations de votre boutique dans Paramètres
3. **Ajouter** vos premiers produits
4. **Créer** vos comptes utilisateurs
5. **Tester** une vente d'exemple

### Données de Démonstration:
L'application inclut des données d'exemple pour vous familiariser avec les fonctionnalités.

## 💾 Sauvegarde des Données

**Important**: Vos données sont stockées localement sur votre ordinateur.

### Localisation des Données:
- **Installation**: \`C:\\Users\\<USER>\\AppData\\Roaming\\SmartBoutique\`
- **Portable**: Dans le même dossier que l'exécutable

### Sauvegarde Recommandée:
- **Copier** régulièrement le dossier de données
- **Utiliser** une clé USB ou cloud pour sauvegardes
- **Tester** la restauration périodiquement

## 🖨️ Configuration Impression

### Imprimantes Supportées:
- **Imprimantes thermiques** (58mm, 80mm)
- **Imprimantes laser/jet d'encre** standard
- **Configuration automatique** des formats

### Test d'Impression:
1. **Aller** dans Paramètres → Impression
2. **Sélectionner** votre imprimante
3. **Imprimer** un reçu de test
4. **Ajuster** les paramètres si nécessaire

## 🔧 Support Technique

### Problèmes Courants:

**L'application ne démarre pas:**
- Vérifier les droits administrateur
- Désactiver temporairement l'antivirus
- Réinstaller avec l'option "Réparer"

**Problèmes d'impression:**
- Vérifier la connexion imprimante
- Mettre à jour les pilotes
- Tester avec une autre imprimante

**Données perdues:**
- Vérifier le dossier de sauvegarde
- Restaurer depuis la dernière sauvegarde
- Contacter le support si nécessaire

### Contact Support:
- **Email**: <EMAIL>
- **Téléphone**: [À définir]
- **Documentation**: Incluse dans l'application

## 📋 Configuration Système

### Exigences Minimales:
- **OS**: Windows 10 ou supérieur
- **RAM**: 4 GB minimum, 8 GB recommandé
- **Espace**: 500 MB d'espace libre
- **Résolution**: 1024x768 minimum

### Recommandations:
- **Antivirus**: Ajouter SmartBoutique aux exceptions
- **Firewall**: Autoriser l'application (pour futures mises à jour)
- **Sauvegarde**: Configurer une sauvegarde automatique

## 🎓 Formation et Utilisation

### Ressources Disponibles:
- **Guide utilisateur** intégré à l'application
- **Tutoriels vidéo** (si disponibles)
- **Support téléphonique** pour formation

### Conseils d'Utilisation:
- **Commencer** par les données de démonstration
- **Tester** toutes les fonctionnalités avant utilisation réelle
- **Former** tous les utilisateurs aux procédures
- **Établir** des routines de sauvegarde

---

**SmartBoutique v${version}**  
*Développé spécialement pour les commerces en RDC*  
*Copyright © 2024 SmartBoutique Team*
`;

  fs.writeFileSync('release-final/CLIENT_INSTRUCTIONS.md', clientInstructions);
  console.log('   ✅ Created CLIENT_INSTRUCTIONS.md');
}
