#!/usr/bin/env node

/**
 * Simple Icon Creator for SmartBoutique
 * Creates a basic SVG icon that can be converted to ICO format
 */

const fs = require('fs');
const path = require('path');

console.log('🎨 Creating SmartBoutique icon...');

// Create a simple SVG icon for SmartBoutique
const svgIcon = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="#2563eb" stroke="#1e40af" stroke-width="8"/>
  
  <!-- Shopping bag shape -->
  <path d="M160 200 L352 200 L340 400 L172 400 Z" fill="#ffffff" stroke="#e5e7eb" stroke-width="2"/>
  
  <!-- Bag handles -->
  <path d="M200 200 C200 170 220 150 256 150 C292 150 312 170 312 200" 
        fill="none" stroke="#ffffff" stroke-width="8" stroke-linecap="round"/>
  
  <!-- SB monogram -->
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="80" font-weight="bold" 
        text-anchor="middle" fill="#2563eb">SB</text>
  
  <!-- Small decorative elements -->
  <circle cx="220" cy="250" r="4" fill="#2563eb"/>
  <circle cx="292" cy="250" r="4" fill="#2563eb"/>
</svg>`;

// Save SVG file
fs.writeFileSync(path.join(__dirname, 'icon.svg'), svgIcon);
console.log('✅ Created build/icon.svg');

// Create a simple PNG placeholder (base64 encoded 1x1 pixel)
const simplePng = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
fs.writeFileSync(path.join(__dirname, 'icon.png'), simplePng);
console.log('✅ Created build/icon.png (placeholder)');

console.log('\n📝 Note: For production, consider creating a professional icon using:');
console.log('   • Online tools: favicon.io, canva.com');
console.log('   • Convert SVG to ICO: convertio.co, cloudconvert.com');
console.log('   • Replace build/icon.ico with a proper multi-size ICO file');

console.log('\n🎯 Icon creation completed!');
